{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\r\n\r\nconst globalForPrisma = globalThis as unknown as {\r\n  prisma: PrismaClient | undefined\r\n}\r\n\r\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\r\n\r\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\r\nimport Credential<PERSON><PERSON>rovider from 'next-auth/providers/credentials'\r\nimport bcrypt from 'bcryptjs'\r\nimport { prisma } from './db'\r\n\r\nexport const authOptions: NextAuthOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null\r\n        }\r\n\r\n        try {\r\n          const user = await prisma.user.findUnique({\r\n            where: {\r\n              email: credentials.email\r\n            }\r\n          })\r\n\r\n          if (!user || !user.hashedPassword) {\r\n            return null\r\n          }\r\n\r\n          const isCorrectPassword = await bcrypt.compare(\r\n            credentials.password,\r\n            user.hashedPassword\r\n          )\r\n\r\n          if (!isCorrectPassword) {\r\n            return null\r\n          }\r\n\r\n          return {\r\n            id: user.id,\r\n            email: user.email,\r\n            name: `${user.firstName} ${user.lastName}`,\r\n            role: user.role,\r\n            firstName: user.firstName,\r\n            lastName: user.lastName\r\n          }\r\n        } catch (error) {\r\n          console.error('Auth error:', error)\r\n          return null\r\n        }\r\n      }\r\n    })\r\n  ],\r\n  session: {\r\n    strategy: 'jwt',\r\n    maxAge: 24 * 60 * 60, // 24 hours\r\n  },\r\n  callbacks: {\r\n    async jwt({ token, user }) {\r\n      if (user) {\r\n        token.role = user.role\r\n        token.firstName = user.firstName\r\n        token.lastName = user.lastName\r\n      }\r\n      return token\r\n    },\r\n    async session({ session, token }) {\r\n      if (token) {\r\n        session.user.id = token.sub!\r\n        session.user.role = token.role as string\r\n        session.user.firstName = token.firstName as string\r\n        session.user.lastName = token.lastName as string\r\n      }\r\n      return session\r\n    }\r\n  },\r\n  pages: {\r\n    signIn: '/login',\r\n    error: '/login'\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAEO,MAAM,cAA+B;IAC1C,WAAW;QACT,IAAA,mTAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,OAAO;4BACL,OAAO,YAAY,KAAK;wBAC1B;oBACF;oBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,EAAE;wBACjC,OAAO;oBACT;oBAEA,MAAM,oBAAoB,MAAM,qOAAM,CAAC,OAAO,CAC5C,YAAY,QAAQ,EACpB,KAAK,cAAc;oBAGrB,IAAI,CAAC,mBAAmB;wBACtB,OAAO;oBACT;oBAEA,OAAO;wBACL,IAAI,KAAK,EAAE;wBACX,OAAO,KAAK,KAAK;wBACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;wBAC1C,MAAM,KAAK,IAAI;wBACf,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK;IACpB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/api/teacher/exams/%5BexamId%5D/students/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/db'\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { examId: string } }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || session.user.role !== 'TEACHER') {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { examId } = params\n\n    // Get the exam details first\n    const exam = await prisma.exam.findUnique({\n      where: { id: examId },\n      include: {\n        subject: {\n          include: {\n            class: true\n          }\n        },\n        term: true\n      }\n    })\n\n    if (!exam) {\n      return NextResponse.json({ error: 'Exam not found' }, { status: 404 })\n    }\n\n    // Get all students in the class for this exam's subject\n    const students = await prisma.student.findMany({\n      where: {\n        currentClassId: exam.subject.classId\n      },\n      include: {\n        user: true,\n        currentClass: true,\n        currentSection: true,\n        marks: {\n          where: {\n            examId: examId\n          }\n        }\n      },\n      orderBy: [\n        { currentSection: { name: 'asc' } },\n        { rollNumber: 'asc' },\n        { user: { firstName: 'asc' } }\n      ]\n    })\n\n    // Transform the data to include mark information\n    const studentsWithMarks = students.map(student => ({\n      id: student.id,\n      admissionNo: student.admissionNo,\n      rollNumber: student.rollNumber,\n      firstName: student.user.firstName,\n      lastName: student.user.lastName,\n      email: student.user.email,\n      className: student.currentClass?.name,\n      sectionName: student.currentSection?.name,\n      currentMark: student.marks[0] || null, // There should be at most one mark per student per exam\n      hasMarks: student.marks.length > 0\n    }))\n\n    return NextResponse.json({\n      exam,\n      students: studentsWithMarks\n    })\n  } catch (error) {\n    console.error('Error fetching exam students:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAkC;IAE1C,IAAI;QACF,MAAM,UAAU,MAAM,IAAA,ySAAgB,EAAC,qKAAW;QAElD,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW;YAC/C,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,MAAM,EAAE,GAAG;QAEnB,6BAA6B;QAC7B,MAAM,OAAO,MAAM,8JAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE,IAAI;YAAO;YACpB,SAAS;gBACP,SAAS;oBACP,SAAS;wBACP,OAAO;oBACT;gBACF;gBACA,MAAM;YACR;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,iSAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,wDAAwD;QACxD,MAAM,WAAW,MAAM,8JAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBACL,gBAAgB,KAAK,OAAO,CAAC,OAAO;YACtC;YACA,SAAS;gBACP,MAAM;gBACN,cAAc;gBACd,gBAAgB;gBAChB,OAAO;oBACL,OAAO;wBACL,QAAQ;oBACV;gBACF;YACF;YACA,SAAS;gBACP;oBAAE,gBAAgB;wBAAE,MAAM;oBAAM;gBAAE;gBAClC;oBAAE,YAAY;gBAAM;gBACpB;oBAAE,MAAM;wBAAE,WAAW;oBAAM;gBAAE;aAC9B;QACH;QAEA,iDAAiD;QACjD,MAAM,oBAAoB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBACjD,IAAI,QAAQ,EAAE;gBACd,aAAa,QAAQ,WAAW;gBAChC,YAAY,QAAQ,UAAU;gBAC9B,WAAW,QAAQ,IAAI,CAAC,SAAS;gBACjC,UAAU,QAAQ,IAAI,CAAC,QAAQ;gBAC/B,OAAO,QAAQ,IAAI,CAAC,KAAK;gBACzB,WAAW,QAAQ,YAAY,EAAE;gBACjC,aAAa,QAAQ,cAAc,EAAE;gBACrC,aAAa,QAAQ,KAAK,CAAC,EAAE,IAAI;gBACjC,UAAU,QAAQ,KAAK,CAAC,MAAM,GAAG;YACnC,CAAC;QAED,OAAO,iSAAY,CAAC,IAAI,CAAC;YACvB;YACA,UAAU;QACZ;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,iSAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}