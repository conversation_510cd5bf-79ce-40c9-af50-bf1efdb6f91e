'use client'

import { useSession } from 'next-auth/react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import DashboardLayout from '@/components/layout/dashboard-layout'
import { studentNavigation } from '@/lib/navigation'
import {
  TrendingUp,
  Bell,
  GraduationCap,
  Target,
  Award,
  Calendar,
  FileText,
  Clock
} from 'lucide-react'

export default function StudentDashboard() {
  const { data: session } = useSession()

  // Mock data - in real app, this would come from API
  const stats = {
    currentClass: 'Grade 8A',
    rollNumber: '8A01',
    attendanceRate: 96.5,
    averageMarks: 85.2,
    totalSubjects: 5,
    upcomingExams: 1
  }

  const subjects = [
    {
      name: 'Mathematics',
      teacher: 'Mr. <PERSON>',
      marks: 92,
      grade: 'A+',
      attendance: 98
    },
    {
      name: 'English',
      teacher: 'Ms. <PERSON>',
      marks: 88,
      grade: 'A',
      attendance: 95
    },
    {
      name: 'Science',
      teacher: 'Dr. <PERSON>',
      marks: 85,
      grade: 'A',
      attendance: 97
    },
    {
      name: 'Social Studies',
      teacher: 'Mrs. <PERSON>',
      marks: 82,
      grade: 'B+',
      attendance: 94
    },
    {
      name: 'Computer Science',
      teacher: 'Mr. <PERSON>',
      marks: 90,
      grade: 'A+',
      attendance: 96
    }
  ]

  const quickActions = [
    {
      title: 'View Marks',
      description: 'Check your latest grades',
      icon: Award,
      href: '/student/marks',
      color: 'bg-blue-500'
    },
    {
      title: 'Attendance History',
      description: 'View your attendance record',
      icon: Calendar,
      href: '/student/attendance',
      color: 'bg-green-500'
    },
    {
      title: 'Download Report',
      description: 'Get your report card',
      icon: FileText,
      href: '/student/reports',
      color: 'bg-purple-500'
    },
    {
      title: 'View Schedule',
      description: 'Check your timetable',
      icon: Clock,
      href: '/student/schedule',
      color: 'bg-orange-500'
    }
  ]

  return (
    <DashboardLayout title="Student Dashboard" navigation={studentNavigation}>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Welcome back, {session?.user?.firstName || 'Student'}!
          </h2>
          <p className="text-gray-600">
            Here's your academic overview and progress summary.
          </p>
        </div>

        {/* Personal Info Card */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Class:</span>
                <span className="font-medium">{stats.currentClass}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Roll Number:</span>
                <span className="font-medium">{stats.rollNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Total Subjects:</span>
                <span className="font-medium">{stats.totalSubjects}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Attendance Rate:</span>
                <span className="font-medium text-green-600">{stats.attendanceRate}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Average Marks:</span>
                <span className="font-medium text-blue-600">{stats.averageMarks}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Upcoming Exams:</span>
                <span className="font-medium">{stats.upcomingExams}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attendance</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.attendanceRate}%</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Marks</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{stats.averageMarks}%</div>
              <p className="text-xs text-muted-foreground">
                Current term
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">GPA</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">3.8</div>
              <p className="text-xs text-muted-foreground">
                Out of 4.0
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Rank</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">5th</div>
              <p className="text-xs text-muted-foreground">
                In class
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Subject Performance */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Subject Performance</h3>
          <div className="space-y-3">
            {subjects.map((subject) => (
              <div key={subject.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <div>
                    <p className="font-medium">{subject.name}</p>
                    <p className="text-sm text-gray-600">{subject.teacher}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Marks</p>
                    <p className="font-medium text-blue-600">{subject.marks}%</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Grade</p>
                    <p className="font-medium text-green-600">{subject.grade}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Attendance</p>
                    <p className="font-medium text-purple-600">{subject.attendance}%</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Card key={action.title} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">{action.title}</h4>
                      <p className="text-xs text-gray-500">{action.description}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Marks uploaded</p>
                <p className="text-xs text-gray-500">Mathematics - Unit Test 1: 92%</p>
              </div>
              <span className="text-xs text-gray-500">2 days ago</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Attendance marked</p>
                <p className="text-xs text-gray-500">Present in all classes today</p>
              </div>
              <span className="text-xs text-gray-500">1 day ago</span>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Report card generated</p>
                <p className="text-xs text-gray-500">Term 1 report available for download</p>
              </div>
              <span className="text-xs text-gray-500">1 week ago</span>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
