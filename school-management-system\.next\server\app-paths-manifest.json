{"/(auth)/login/page": "app/(auth)/login/page.js", "/(dash)/admin/marks/page": "app/(dash)/admin/marks/page.js", "/(dash)/admin/page": "app/(dash)/admin/page.js", "/(dash)/admin/students/page": "app/(dash)/admin/students/page.js", "/(dash)/admin/subjects/page": "app/(dash)/admin/subjects/page.js", "/(dash)/student/marks/page": "app/(dash)/student/marks/page.js", "/(dash)/student/page": "app/(dash)/student/page.js", "/(dash)/teacher/marks/[examId]/page": "app/(dash)/teacher/marks/[examId]/page.js", "/(dash)/teacher/marks/page": "app/(dash)/teacher/marks/page.js", "/(dash)/teacher/page": "app/(dash)/teacher/page.js", "/_not-found/page": "app/_not-found/page.js", "/api/admin/subjects/route": "app/api/admin/subjects/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/student/marks/route": "app/api/student/marks/route.js", "/api/teacher/exams/[examId]/students/route": "app/api/teacher/exams/[examId]/students/route.js", "/api/teacher/exams/route": "app/api/teacher/exams/route.js", "/api/teacher/marks/route": "app/api/teacher/marks/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js", "/unauthorized/page": "app/unauthorized/page.js"}