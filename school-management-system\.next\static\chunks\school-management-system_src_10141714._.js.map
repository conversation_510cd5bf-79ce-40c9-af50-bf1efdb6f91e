{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yQAAO,EAAC,IAAA,mOAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h3\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <p\r\n    ref={ref}\r\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,4TAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,+HACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,4TAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4TAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4TAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4CAA4C;QACzD,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4TAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QAAI,KAAK;QAAK,WAAW,IAAA,8JAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4TAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC;QACC,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700\",\r\n        destructive:\r\n          \"bg-red-600 text-white hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-700\",\r\n        outline:\r\n          \"border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        secondary:\r\n          \"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-200 dark:hover:bg-gray-700\",\r\n        ghost: \"hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-gray-100\",\r\n        link: \"text-blue-600 dark:text-blue-400 underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uRAAG,EACxB,uQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kVAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'\nimport { Check, ChevronRight, Circle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      'flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName = DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName = DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName = DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      'px-2 py-1.5 text-sm font-semibold',\n      inset && 'pl-8',\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn('-mx-1 my-1 h-px bg-muted', className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn('ml-auto text-xs tracking-widest opacity-60', className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = 'DropdownMenuShortcut'\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,eAAe,gUAA0B;AAE/C,MAAM,sBAAsB,mUAA6B;AAEzD,MAAM,oBAAoB,iUAA2B;AAErD,MAAM,qBAAqB,kUAA4B;AAEvD,MAAM,kBAAkB,+TAAyB;AAEjD,MAAM,yBAAyB,sUAAgC;AAE/D,MAAM,uCAAyB,4TAAgB,MAK7C,QAA2C;QAA1C,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO;yBACzC,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8UAAC,+VAAY;gBAAC,WAAU;;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,uCAAyB,4TAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,sUAAgC;QAC/B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,ybACA;QAED,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAAG,sUAAgC,CAAC,WAAW;AAEjF,MAAM,oCAAsB,4TAAgB,OAG1C,QAA0C;QAAzC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO;yBACxC,8UAAC,kUAA4B;kBAC3B,cAAA,8UAAC,mUAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,IAAA,8JAAE,EACX,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,mUAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,4TAAgB,OAKvC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,gUAA0B;QACzB,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gUAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,4TAAgB,OAG/C,QAA6C;QAA5C,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO;yBAC3C,8UAAC,wUAAkC;QACjC,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,sUAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;;AAGL,yBAAyB,WAAW,GAAG,wUAAkC,CAAC,WAAW;AAErF,MAAM,sCAAwB,4TAAgB,QAG5C,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,wOACA;QAED,GAAG,KAAK;;0BAET,8UAAC;gBAAK,WAAU;0BACd,cAAA,8UAAC,yUAAmC;8BAClC,cAAA,8UAAC,yUAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,4TAAgB,QAKxC,QAAiC;QAAhC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO;yBAC/B,8UAAC,iUAA2B;QAC1B,KAAK;QACL,WAAW,IAAA,8JAAE,EACX,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,iUAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,4TAAgB,QAG5C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,qUAA+B;QAC9B,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAGb,sBAAsB,WAAW,GAAG,qUAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB;QAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8UAAC;QACC,WAAW,IAAA,8JAAE,EAAC,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Sun, Monitor } from 'lucide-react'\nimport { Button } from './button'\nimport { useTheme } from '@/components/providers/theme-provider'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from './dropdown-menu'\n\nexport function ThemeToggle() {\n  const { theme, actualTheme, mounted, setTheme } = useTheme()\n\n  const toggleTheme = () => {\n    if (!mounted) return\n    const next = actualTheme === 'light' ? 'dark' : 'light'\n    setTheme(next)\n  }\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-9 w-9\"\n          onClick={toggleTheme}\n          title={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n        >\n          <Sun className=\"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme('light')}>\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('dark')}>\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme('system')}>\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAA,qMAAQ;IAE1D,MAAM,cAAc;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,OAAO,gBAAgB,UAAU,SAAS;QAChD,SAAS;IACX;IAEA,qBACE,8UAAC,iMAAY;;0BACX,8UAAC,wMAAmB;gBAAC,OAAO;0BAC1B,cAAA,8UAAC,iLAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAO,AAAC,aAAuD,OAA3C,gBAAgB,UAAU,SAAS,SAAQ;;sCAE/D,8UAAC,gUAAG;4BAAC,WAAU;;;;;;sCACf,8UAAC,mUAAI;4BAAC,WAAU;;;;;;sCAChB,8UAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8UAAC,wMAAmB;gBAAC,OAAM;;kCACzB,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,gUAAG;gCAAC,WAAU;;;;;;0CACf,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,mUAAI;gCAAC,WAAU;;;;;;0CAChB,8UAAC;0CAAK;;;;;;;;;;;;kCAER,8UAAC,qMAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8UAAC,4UAAO;gCAAC,WAAU;;;;;;0CACnB,8UAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GAxCgB;;QACoC,qMAAQ;;;KAD5C", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useSession, signOut } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\n\nimport { ThemeToggle } from '@/components/ui/theme-toggle'\nimport {\n  Menu,\n  X,\n  User,\n  LogOut,\n  Settings,\n  Bell,\n  Search,\n  School,\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Edit,\n  ClipboardList,\n  Award\n} from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n  title: string\n  navigation: {\n    name: string\n    href: string\n    icon: string\n  }[]\n}\n\n// Icon mapping object\nconst iconMap: Record<string, React.ComponentType<{ className?: string }>> = {\n  Plus,\n  Upload,\n  Download,\n  Users,\n  BookOpen,\n  GraduationCap,\n  FileText,\n  BarChart3,\n  Calendar,\n  Home,\n  Settings,\n  Bell,\n  User,\n  Edit,\n  ClipboardList,\n  Award\n}\n\nexport default function DashboardLayout({ children, title, navigation }: DashboardLayoutProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' })\n  }\n\n  const getIcon = (iconName: string) => {\n    const IconComponent = iconMap[iconName]\n    return IconComponent || Home // fallback to Home icon if not found\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-950\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 left-0 flex w-64 flex-col bg-white dark:bg-gray-900\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <div className=\"flex items-center\">\n              <School className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold\">SMS</span>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setSidebarOpen(false)}\n            >\n              <X className=\"h-5 w-5\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`mobile-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => {\n                    router.push(item.href)\n                    setSidebarOpen(false)\n                  }}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col\">\n        <div className=\"flex flex-col flex-grow bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\">\n          <div className=\"flex h-16 items-center px-4\">\n            <School className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"ml-2 text-lg font-semibold hidden xl:inline\">School Management System</span>\n            <span className=\"ml-2 text-lg font-semibold xl:hidden\">SMS</span>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-2 py-4\">\n            {navigation.map((item) => {\n              const IconComponent = getIcon(item.icon)\n              return (\n                <Button\n                  key={`desktop-${item.name}`}\n                  variant=\"ghost\"\n                  className=\"w-full justify-start\"\n                  onClick={() => router.push(item.href)}\n                >\n                  <IconComponent className=\"mr-3 h-5 w-5\" />\n                  {item.name}\n                </Button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"relative flex flex-1\">\n              <div className=\"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3\">\n                <Search className=\"h-5 w-5 text-gray-400\" />\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search...\"\n                className=\"block h-full w-full border-0 py-0 pl-10 pr-0 text-gray-900 dark:text-gray-100 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-0 sm:text-sm bg-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n            <ThemeToggle />\n\n            <Button variant=\"ghost\" size=\"sm\">\n              <Bell className=\"h-5 w-5\" />\n            </Button>\n\n            <div className=\"relative\">\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-sm hidden sm:block\">\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\n                    {session?.user?.firstName} {session?.user?.lastName}\n                  </p>\n                  <p className=\"text-gray-500 dark:text-gray-400 capitalize\">\n                    {session?.user?.role?.toLowerCase()}\n                  </p>\n                </div>\n                <div className=\"flex items-center gap-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleSignOut}\n                    className=\"flex items-center gap-2\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100\">{title}</h1>\n            </div>\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AA0CA,sBAAsB;AACtB,MAAM,UAAuE;IAC3E,MAAA,mUAAI;IACJ,QAAA,yUAAM;IACN,UAAA,+UAAQ;IACR,OAAA,sUAAK;IACL,UAAA,mVAAQ;IACR,eAAA,kWAAa;IACb,UAAA,mVAAQ;IACR,WAAA,wVAAS;IACT,UAAA,+UAAQ;IACR,MAAA,oUAAI;IACJ,UAAA,+UAAQ;IACR,MAAA,mUAAI;IACJ,MAAA,mUAAI;IACJ,MAAA,4UAAI;IACJ,eAAA,kWAAa;IACb,OAAA,sUAAK;AACP;AAEe,SAAS,gBAAgB,KAAqD;QAArD,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAwB,GAArD;QAuHnB,eAA2B,gBAG3B,oBAAA;;IAzHnB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IACpC,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,0TAAQ,EAAC;IAE/C,MAAM,gBAAgB;QACpB,MAAM,IAAA,0SAAO,EAAC;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,UAAU,CAAC;QACf,MAAM,gBAAgB,OAAO,CAAC,SAAS;QACvC,OAAO,iBAAiB,oUAAI,CAAC,qCAAqC;;IACpE;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC;gBAAI,WAAW,AAAC,gCAAgE,OAAjC,cAAc,UAAU;;kCACtE,8UAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,yUAAM;gDAAC,WAAU;;;;;;0DAClB,8UAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;kDAE/C,8UAAC,iLAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;kDAE9B,cAAA,8UAAC,0TAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8UAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC;oCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;oCACvC,qBACE,8UAAC,iLAAM;wCAEL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,OAAO,IAAI,CAAC,KAAK,IAAI;4CACrB,eAAe;wCACjB;;0DAEA,8UAAC;gDAAc,WAAU;;;;;;4CACxB,KAAK,IAAI;;uCATL,AAAC,UAAmB,OAAV,KAAK,IAAI;;;;;gCAY9B;;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;0BACb,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,yUAAM;oCAAC,WAAU;;;;;;8CAClB,8UAAC;oCAAK,WAAU;8CAA8C;;;;;;8CAC9D,8UAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;sCAEzD,8UAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC;gCACf,MAAM,gBAAgB,QAAQ,KAAK,IAAI;gCACvC,qBACE,8UAAC,iLAAM;oCAEL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;sDAEpC,8UAAC;4CAAc,WAAU;;;;;;wCACxB,KAAK,IAAI;;mCANL,AAAC,WAAoB,OAAV,KAAK,IAAI;;;;;4BAS/B;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAI,WAAU;;kCAEb,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,iLAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8UAAC,mUAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8UAAC;gCAAI,WAAU;0CACb,cAAA,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC,yUAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8UAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;0CAKhB,8UAAC;gCAAI,WAAU;;kDACb,8UAAC,+LAAW;;;;;kDAEZ,8UAAC,iLAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,8UAAC,mUAAI;4CAAC,WAAU;;;;;;;;;;;kDAGlB,8UAAC;wCAAI,WAAU;kDACb,cAAA,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC;4DAAE,WAAU;;gEACV,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,SAAS;gEAAC;gEAAE,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,QAAQ;;;;;;;sEAErD,8UAAC;4DAAE,WAAU;sEACV,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,sCAAA,qBAAA,eAAe,IAAI,cAAnB,yCAAA,mBAAqB,WAAW;;;;;;;;;;;;8DAGrC,8UAAC;oDAAI,WAAU;8DACb,cAAA,8UAAC,iLAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;;0EAEV,8UAAC,6UAAM;gEAAC,WAAU;;;;;;0EAClB,8UAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/C,8UAAC;wBAAK,WAAU;kCACd,cAAA,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAI,WAAU;8CACb,cAAA,8UAAC;wCAAG,WAAU;kDAAuD;;;;;;;;;;;gCAEtE;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAzJwB;;QACI,6SAAU;QACrB,mSAAS;;;KAFF", "debugId": null}}, {"offset": {"line": 1154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/navigation.ts"], "sourcesContent": ["// Shared navigation configurations for different user roles\n\nexport const adminNavigation = [\n  { name: 'Dashboard', href: '/admin', icon: 'BarChart3' },\n  { name: 'Students', href: '/admin/students', icon: 'Users' },\n  { name: 'Teachers', href: '/admin/teachers', icon: 'GraduationCap' },\n  { name: 'Classes & Sections', href: '/admin/classes', icon: 'BookOpen' },\n  { name: 'Subjects', href: '/admin/subjects', icon: 'FileText' },\n  { name: 'Terms & Exams', href: '/admin/exams', icon: 'Calendar' },\n  { name: 'Attendance', href: '/admin/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/admin/marks', icon: 'Award' },\n  { name: 'Reports', href: '/admin/reports', icon: 'FileText' },\n  { name: 'Settings', href: '/admin/settings', icon: 'Settings' },\n];\n\nexport const teacherNavigation = [\n  { name: 'Dashboard', href: '/teacher', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/teacher/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/teacher/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/teacher/marks', icon: 'Award' },\n  { name: 'Students', href: '/teacher/students', icon: 'Users' },\n  { name: 'Reports', href: '/teacher/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/teacher/profile', icon: 'User' },\n];\n\nexport const studentNavigation = [\n  { name: 'Dashboard', href: '/student', icon: 'BarChart3' },\n  { name: 'My Classes', href: '/student/classes', icon: 'BookOpen' },\n  { name: 'Attendance', href: '/student/attendance', icon: 'ClipboardList' },\n  { name: 'Marks', href: '/student/marks', icon: 'Award' },\n  { name: 'Reports', href: '/student/reports', icon: 'FileText' },\n  { name: 'Profile', href: '/student/profile', icon: 'User' },\n];\n\n/**\n * Get the default dashboard URL for a user role\n */\nexport function getRoleDashboardUrl(role: string): string {\n  switch (role) {\n    case 'ADMIN':\n      return '/admin'\n    case 'TEACHER':\n      return '/teacher'\n    case 'STUDENT':\n      return '/student'\n    default:\n      return '/'\n  }\n}\n\n/**\n * Get navigation items for a user role\n */\nexport function getRoleNavigation(role: string) {\n  switch (role) {\n    case 'ADMIN':\n      return adminNavigation\n    case 'TEACHER':\n      return teacherNavigation\n    case 'STUDENT':\n      return studentNavigation\n    default:\n      return []\n  }\n}"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;;;;;AAErD,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAa,MAAM;QAAU,MAAM;IAAY;IACvD;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAQ;IAC3D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAgB;IACnE;QAAE,MAAM;QAAsB,MAAM;QAAkB,MAAM;IAAW;IACvE;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAiB,MAAM;QAAgB,MAAM;IAAW;IAChE;QAAE,MAAM;QAAc,MAAM;QAAqB,MAAM;IAAgB;IACvE;QAAE,MAAM;QAAS,MAAM;QAAgB,MAAM;IAAQ;IACrD;QAAE,MAAM;QAAW,MAAM;QAAkB,MAAM;IAAW;IAC5D;QAAE,MAAM;QAAY,MAAM;QAAmB,MAAM;IAAW;CAC/D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAY,MAAM;QAAqB,MAAM;IAAQ;IAC7D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAEM,MAAM,oBAAoB;IAC/B;QAAE,MAAM;QAAa,MAAM;QAAY,MAAM;IAAY;IACzD;QAAE,MAAM;QAAc,MAAM;QAAoB,MAAM;IAAW;IACjE;QAAE,MAAM;QAAc,MAAM;QAAuB,MAAM;IAAgB;IACzE;QAAE,MAAM;QAAS,MAAM;QAAkB,MAAM;IAAQ;IACvD;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAW;IAC9D;QAAE,MAAM;QAAW,MAAM;QAAoB,MAAM;IAAO;CAC3D;AAKM,SAAS,oBAAoB,IAAY;IAC9C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,kBAAkB,IAAY;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nexport interface InputProps\r\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 px-3 py-2 text-sm text-gray-900 dark:text-gray-100 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,4TAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,8UAAC;QACC,MAAM;QACN,WAAW,IAAA,8JAAE,EACX,yaACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,IAAA,uRAAG,EACvB;AAGF,MAAM,sBAAQ,4TAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,2TAAmB;QAClB,KAAK;QACL,WAAW,IAAA,8JAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,2TAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/lib/marks-validation.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Validation schemas\nexport const MarkEntrySchema = z.object({\n  studentId: z.string().min(1, 'Student ID is required'),\n  examId: z.string().min(1, 'Exam ID is required'),\n  obtainedMarks: z.number()\n    .min(0, 'Marks cannot be negative')\n    .max(1000, 'Marks cannot exceed 1000'), // Will be validated against exam maxMarks\n  remarks: z.string().optional()\n})\n\nexport const BulkMarkEntrySchema = z.object({\n  examId: z.string().min(1, 'Exam ID is required'),\n  marks: z.array(z.object({\n    studentId: z.string().min(1, 'Student ID is required'),\n    obtainedMarks: z.number()\n      .min(0, 'Marks cannot be negative')\n      .max(1000, 'Marks cannot exceed 1000'),\n    remarks: z.string().optional()\n  })).min(1, 'At least one mark entry is required')\n})\n\n// Validation functions\nexport interface ValidationError {\n  field: string\n  message: string\n  studentId?: string\n}\n\nexport interface MarkValidationResult {\n  isValid: boolean\n  errors: ValidationError[]\n}\n\nexport const validateMarkEntry = (\n  studentId: string,\n  examId: string,\n  obtainedMarks: number,\n  maxMarks: number,\n  remarks?: string\n): MarkValidationResult => {\n  const errors: ValidationError[] = []\n\n  // Basic validation\n  if (!studentId || studentId.trim() === '') {\n    errors.push({ field: 'studentId', message: 'Student ID is required' })\n  }\n\n  if (!examId || examId.trim() === '') {\n    errors.push({ field: 'examId', message: 'Exam ID is required' })\n  }\n\n  // Marks validation\n  if (obtainedMarks < 0) {\n    errors.push({ \n      field: 'obtainedMarks', \n      message: 'Marks cannot be negative',\n      studentId \n    })\n  }\n\n  if (obtainedMarks > maxMarks) {\n    errors.push({ \n      field: 'obtainedMarks', \n      message: `Marks cannot exceed maximum marks (${maxMarks})`,\n      studentId \n    })\n  }\n\n  // Check for decimal precision (max 2 decimal places)\n  if (obtainedMarks % 0.01 !== 0) {\n    errors.push({ \n      field: 'obtainedMarks', \n      message: 'Marks can have at most 2 decimal places',\n      studentId \n    })\n  }\n\n  // Remarks validation (optional but if provided, should be reasonable length)\n  if (remarks && remarks.length > 500) {\n    errors.push({ \n      field: 'remarks', \n      message: 'Remarks cannot exceed 500 characters',\n      studentId \n    })\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\nexport const validateBulkMarkEntry = (\n  examId: string,\n  maxMarks: number,\n  marksData: Array<{\n    studentId: string\n    obtainedMarks: number\n    remarks?: string\n  }>\n): MarkValidationResult => {\n  const errors: ValidationError[] = []\n\n  // Exam validation\n  if (!examId || examId.trim() === '') {\n    errors.push({ field: 'examId', message: 'Exam ID is required' })\n  }\n\n  // Check if marks data is provided\n  if (!marksData || marksData.length === 0) {\n    errors.push({ field: 'marks', message: 'At least one mark entry is required' })\n    return { isValid: false, errors }\n  }\n\n  // Validate each mark entry\n  const studentIds = new Set<string>()\n  marksData.forEach((mark, index) => {\n    // Check for duplicate student IDs\n    if (studentIds.has(mark.studentId)) {\n      errors.push({ \n        field: 'studentId', \n        message: 'Duplicate student ID found',\n        studentId: mark.studentId \n      })\n    } else {\n      studentIds.add(mark.studentId)\n    }\n\n    // Validate individual mark entry\n    const validation = validateMarkEntry(\n      mark.studentId,\n      examId,\n      mark.obtainedMarks,\n      maxMarks,\n      mark.remarks\n    )\n\n    // Add any errors from individual validation\n    errors.push(...validation.errors)\n  })\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n// Grade calculation validation\nexport const validateGradeCalculation = (\n  obtainedMarks: number,\n  maxMarks: number\n): { isValid: boolean; percentage?: number; error?: string } => {\n  if (maxMarks <= 0) {\n    return { isValid: false, error: 'Maximum marks must be greater than 0' }\n  }\n\n  if (obtainedMarks < 0) {\n    return { isValid: false, error: 'Obtained marks cannot be negative' }\n  }\n\n  if (obtainedMarks > maxMarks) {\n    return { isValid: false, error: 'Obtained marks cannot exceed maximum marks' }\n  }\n\n  const percentage = Math.round((obtainedMarks / maxMarks) * 100 * 100) / 100\n  return { isValid: true, percentage }\n}\n\n// Error formatting utilities\nexport const formatValidationErrors = (errors: ValidationError[]): string => {\n  if (errors.length === 0) return ''\n  \n  if (errors.length === 1) {\n    return errors[0].message\n  }\n\n  return `Multiple errors found:\\n${errors.map(e => `• ${e.message}`).join('\\n')}`\n}\n\nexport const groupErrorsByStudent = (errors: ValidationError[]): Record<string, ValidationError[]> => {\n  return errors.reduce((acc, error) => {\n    const key = error.studentId || 'general'\n    if (!acc[key]) {\n      acc[key] = []\n    }\n    acc[key].push(error)\n    return acc\n  }, {} as Record<string, ValidationError[]>)\n}\n\n// Common validation patterns\nexport const VALIDATION_PATTERNS = {\n  STUDENT_ID: /^[a-zA-Z0-9-_]+$/,\n  EXAM_ID: /^[a-zA-Z0-9-_]+$/,\n  MARKS_FORMAT: /^\\d+(\\.\\d{1,2})?$/, // Allows up to 2 decimal places\n} as const\n\nexport const validatePattern = (value: string, pattern: RegExp, fieldName: string): ValidationError | null => {\n  if (!pattern.test(value)) {\n    return {\n      field: fieldName,\n      message: `Invalid ${fieldName} format`\n    }\n  }\n  return null\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,kBAAkB,uQAAC,CAAC,MAAM,CAAC;IACtC,WAAW,uQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,QAAQ,uQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,eAAe,uQAAC,CAAC,MAAM,GACpB,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,MAAM;IACb,SAAS,uQAAC,CAAC,MAAM,GAAG,QAAQ;AAC9B;AAEO,MAAM,sBAAsB,uQAAC,CAAC,MAAM,CAAC;IAC1C,QAAQ,uQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,OAAO,uQAAC,CAAC,KAAK,CAAC,uQAAC,CAAC,MAAM,CAAC;QACtB,WAAW,uQAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;QAC7B,eAAe,uQAAC,CAAC,MAAM,GACpB,GAAG,CAAC,GAAG,4BACP,GAAG,CAAC,MAAM;QACb,SAAS,uQAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,IAAI,GAAG,CAAC,GAAG;AACb;AAcO,MAAM,oBAAoB,CAC/B,WACA,QACA,eACA,UACA;IAEA,MAAM,SAA4B,EAAE;IAEpC,mBAAmB;IACnB,IAAI,CAAC,aAAa,UAAU,IAAI,OAAO,IAAI;QACzC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAa,SAAS;QAAyB;IACtE;IAEA,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI;QACnC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAU,SAAS;QAAsB;IAChE;IAEA,mBAAmB;IACnB,IAAI,gBAAgB,GAAG;QACrB,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS;YACT;QACF;IACF;IAEA,IAAI,gBAAgB,UAAU;QAC5B,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS,AAAC,sCAA8C,OAAT,UAAS;YACxD;QACF;IACF;IAEA,qDAAqD;IACrD,IAAI,gBAAgB,SAAS,GAAG;QAC9B,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS;YACT;QACF;IACF;IAEA,6EAA6E;IAC7E,IAAI,WAAW,QAAQ,MAAM,GAAG,KAAK;QACnC,OAAO,IAAI,CAAC;YACV,OAAO;YACP,SAAS;YACT;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,MAAM,wBAAwB,CACnC,QACA,UACA;IAMA,MAAM,SAA4B,EAAE;IAEpC,kBAAkB;IAClB,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI;QACnC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAU,SAAS;QAAsB;IAChE;IAEA,kCAAkC;IAClC,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,OAAO,IAAI,CAAC;YAAE,OAAO;YAAS,SAAS;QAAsC;QAC7E,OAAO;YAAE,SAAS;YAAO;QAAO;IAClC;IAEA,2BAA2B;IAC3B,MAAM,aAAa,IAAI;IACvB,UAAU,OAAO,CAAC,CAAC,MAAM;QACvB,kCAAkC;QAClC,IAAI,WAAW,GAAG,CAAC,KAAK,SAAS,GAAG;YAClC,OAAO,IAAI,CAAC;gBACV,OAAO;gBACP,SAAS;gBACT,WAAW,KAAK,SAAS;YAC3B;QACF,OAAO;YACL,WAAW,GAAG,CAAC,KAAK,SAAS;QAC/B;QAEA,iCAAiC;QACjC,MAAM,aAAa,kBACjB,KAAK,SAAS,EACd,QACA,KAAK,aAAa,EAClB,UACA,KAAK,OAAO;QAGd,4CAA4C;QAC5C,OAAO,IAAI,IAAI,WAAW,MAAM;IAClC;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,2BAA2B,CACtC,eACA;IAEA,IAAI,YAAY,GAAG;QACjB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuC;IACzE;IAEA,IAAI,gBAAgB,GAAG;QACrB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,IAAI,gBAAgB,UAAU;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;QAA6C;IAC/E;IAEA,MAAM,aAAa,KAAK,KAAK,CAAC,AAAC,gBAAgB,WAAY,MAAM,OAAO;IACxE,OAAO;QAAE,SAAS;QAAM;IAAW;AACrC;AAGO,MAAM,yBAAyB,CAAC;IACrC,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,OAAO,MAAM,CAAC,EAAE,CAAC,OAAO;IAC1B;IAEA,OAAO,AAAC,2BAAuE,OAA7C,OAAO,GAAG,CAAC,CAAA,IAAK,AAAC,KAAc,OAAV,EAAE,OAAO,GAAI,IAAI,CAAC;AAC3E;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK;QACzB,MAAM,MAAM,MAAM,SAAS,IAAI;QAC/B,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,GAAG,CAAC,IAAI,GAAG,EAAE;QACf;QACA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QACd,OAAO;IACT,GAAG,CAAC;AACN;AAGO,MAAM,sBAAsB;IACjC,YAAY;IACZ,SAAS;IACT,cAAc;AAChB;AAEO,MAAM,kBAAkB,CAAC,OAAe,SAAiB;IAC9D,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ;QACxB,OAAO;YACL,OAAO;YACP,SAAS,AAAC,WAAoB,OAAV,WAAU;QAChC;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/components/marks/marks-entry-form.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { validateMarkEntry, formatValidationErrors, ValidationError } from '@/lib/marks-validation'\nimport {\n  Users,\n  Save,\n  CheckCircle,\n  AlertCircle,\n  Info\n} from 'lucide-react'\n\ninterface Student {\n  id: string\n  admissionNo: string\n  rollNumber: string\n  firstName: string\n  lastName: string\n  email: string\n  className: string\n  sectionName: string\n  currentMark: {\n    id: string\n    obtainedMarks: number\n    remarks?: string\n  } | null\n  hasMarks: boolean\n}\n\ninterface Exam {\n  id: string\n  name: string\n  maxMarks: number\n  date: string\n  subject: {\n    id: string\n    name: string\n    code: string\n    class: {\n      id: string\n      name: string\n    }\n  }\n  term: {\n    id: string\n    name: string\n  }\n}\n\ninterface MarksEntryFormProps {\n  exam: Exam\n  students: Student[]\n  onSave: (marks: Array<{studentId: string, obtainedMarks: number, remarks?: string}>) => Promise<void>\n  saving?: boolean\n}\n\nexport default function MarksEntryForm({ exam, students, onSave, saving = false }: MarksEntryFormProps) {\n  const [marks, setMarks] = useState<Record<string, { obtainedMarks: string, remarks: string }>>(() => {\n    const initialMarks: Record<string, { obtainedMarks: string, remarks: string }> = {}\n    students.forEach((student) => {\n      initialMarks[student.id] = {\n        obtainedMarks: student.currentMark?.obtainedMarks?.toString() || '',\n        remarks: student.currentMark?.remarks || ''\n      }\n    })\n    return initialMarks\n  })\n  const [errors, setErrors] = useState<Record<string, ValidationError[]>>({})\n  const [generalError, setGeneralError] = useState<string>('')\n\n  const handleMarkChange = (studentId: string, field: 'obtainedMarks' | 'remarks', value: string) => {\n    setMarks(prev => ({\n      ...prev,\n      [studentId]: {\n        ...prev[studentId],\n        [field]: value\n      }\n    }))\n\n    // Clear errors when user starts typing\n    if (errors[studentId]) {\n      setErrors(prev => {\n        const newErrors = { ...prev }\n        delete newErrors[studentId]\n        return newErrors\n      })\n    }\n\n    // Clear general error\n    if (generalError) {\n      setGeneralError('')\n    }\n  }\n\n  const validateMarks = () => {\n    const newErrors: Record<string, ValidationError[]> = {}\n    let hasErrors = false\n\n    Object.entries(marks).forEach(([studentId, mark]) => {\n      if (mark.obtainedMarks !== '') {\n        const obtainedMarks = parseFloat(mark.obtainedMarks)\n        if (!isNaN(obtainedMarks)) {\n          const validation = validateMarkEntry(\n            studentId,\n            exam.id,\n            obtainedMarks,\n            exam.maxMarks,\n            mark.remarks\n          )\n\n          if (!validation.isValid) {\n            newErrors[studentId] = validation.errors\n            hasErrors = true\n          }\n        } else {\n          newErrors[studentId] = [{ field: 'obtainedMarks', message: 'Invalid marks format' }]\n          hasErrors = true\n        }\n      }\n    })\n\n    setErrors(newErrors)\n    return !hasErrors\n  }\n\n  const handleSave = async () => {\n    // Clear previous errors\n    setGeneralError('')\n\n    if (!validateMarks()) {\n      setGeneralError('Please fix the validation errors before saving.')\n      return\n    }\n\n    // Prepare marks data for submission\n    const marksData = Object.entries(marks)\n      .filter(([_, mark]) => mark.obtainedMarks !== '')\n      .map(([studentId, mark]) => ({\n        studentId,\n        obtainedMarks: parseFloat(mark.obtainedMarks),\n        remarks: mark.remarks || undefined\n      }))\n\n    if (marksData.length === 0) {\n      setGeneralError('No marks to save. Please enter marks for at least one student.')\n      return\n    }\n\n    try {\n      await onSave(marksData)\n    } catch (error) {\n      setGeneralError('Failed to save marks. Please try again.')\n      console.error('Error saving marks:', error)\n    }\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <Users className=\"w-5 h-5\" />\n          <span>Students ({students.length})</span>\n        </CardTitle>\n        <CardDescription>\n          Enter marks for each student. Maximum marks: {exam.maxMarks}\n        </CardDescription>\n\n        {/* General Error Message */}\n        {generalError && (\n          <div className=\"flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md\">\n            <AlertCircle className=\"w-4 h-4 text-red-600\" />\n            <span className=\"text-sm text-red-700\">{generalError}</span>\n          </div>\n        )}\n\n        {/* Validation Info */}\n        <div className=\"flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-md\">\n          <Info className=\"w-4 h-4 text-blue-600\" />\n          <span className=\"text-sm text-blue-700\">\n            Marks can be entered with up to 2 decimal places (e.g., 85.5). Leave blank to skip a student.\n          </span>\n        </div>\n\n        <div className=\"flex justify-end\">\n          <Button onClick={handleSave} disabled={saving}>\n            <Save className=\"w-4 h-4 mr-2\" />\n            {saving ? 'Saving...' : 'Save All Marks'}\n          </Button>\n        </div>\n      </CardHeader>\n      <CardContent>\n        {students.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-600\">No students found for this exam</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {/* Desktop Table */}\n            <div className=\"hidden lg:block overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Student\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Roll No.\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Section\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Marks (/{exam.maxMarks})\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Remarks\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {students.map((student) => {\n                    const studentErrors = errors[student.id] || []\n                    const hasErrors = studentErrors.length > 0\n\n                    return (\n                      <tr key={student.id} className={hasErrors ? 'bg-red-50' : ''}>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {student.firstName} {student.lastName}\n                            </div>\n                            <div className=\"text-sm text-gray-500\">\n                              {student.admissionNo}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {student.rollNumber || '-'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                          {student.sectionName || '-'}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <Input\n                            type=\"number\"\n                            min=\"0\"\n                            max={exam.maxMarks}\n                            step=\"0.01\"\n                            value={marks[student.id]?.obtainedMarks || ''}\n                            onChange={(e) => handleMarkChange(student.id, 'obtainedMarks', e.target.value)}\n                            className={`w-20 ${hasErrors ? 'border-red-500' : ''}`}\n                            placeholder=\"0\"\n                          />\n                          {hasErrors && (\n                            <div className=\"text-xs text-red-500 mt-1\">\n                              {studentErrors.map((error, index) => (\n                                <div key={index}>{error.message}</div>\n                              ))}\n                            </div>\n                          )}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <Input\n                            type=\"text\"\n                            value={marks[student.id]?.remarks || ''}\n                            onChange={(e) => handleMarkChange(student.id, 'remarks', e.target.value)}\n                            className=\"w-32\"\n                            placeholder=\"Optional\"\n                            maxLength={500}\n                          />\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          {student.hasMarks ? (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\n                              <CheckCircle className=\"w-3 h-3 mr-1\" />\n                              Graded\n                            </span>\n                          ) : (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\">\n                              Pending\n                            </span>\n                          )}\n                        </td>\n                      </tr>\n                    )\n                  })}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Mobile Cards */}\n            <div className=\"lg:hidden space-y-4\">\n              {students.map((student) => {\n                const studentErrors = errors[student.id] || []\n                const hasErrors = studentErrors.length > 0\n\n                return (\n                  <Card key={student.id} className={`p-4 ${hasErrors ? 'border-red-500 bg-red-50' : ''}`}>\n                    <div className=\"space-y-4\">\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1 min-w-0\">\n                          <h3 className=\"text-lg font-medium text-gray-900 truncate\">\n                            {student.firstName} {student.lastName}\n                          </h3>\n                          <p className=\"text-sm text-gray-500\">\n                            {student.admissionNo} • Roll: {student.rollNumber || '-'} • Section: {student.sectionName || '-'}\n                          </p>\n                        </div>\n                        <div className=\"ml-4\">\n                          {student.hasMarks ? (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">\n                              <CheckCircle className=\"w-3 h-3 mr-1\" />\n                              Graded\n                            </span>\n                          ) : (\n                            <span className=\"inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\">\n                              Pending\n                            </span>\n                          )}\n                        </div>\n                      </div>\n\n                      <div className=\"grid grid-cols-2 gap-4\">\n                        <div>\n                          <Label htmlFor={`marks-${student.id}`} className=\"text-sm font-medium text-gray-700\">\n                            Marks (/{exam.maxMarks})\n                          </Label>\n                          <Input\n                            id={`marks-${student.id}`}\n                            type=\"number\"\n                            min=\"0\"\n                            max={exam.maxMarks}\n                            step=\"0.01\"\n                            value={marks[student.id]?.obtainedMarks || ''}\n                            onChange={(e) => handleMarkChange(student.id, 'obtainedMarks', e.target.value)}\n                            className={`mt-1 ${hasErrors ? 'border-red-500' : ''}`}\n                            placeholder=\"0\"\n                          />\n                          {hasErrors && (\n                            <div className=\"text-xs text-red-500 mt-1\">\n                              {studentErrors.map((error, index) => (\n                                <div key={index}>{error.message}</div>\n                              ))}\n                            </div>\n                          )}\n                        </div>\n                        <div>\n                          <Label htmlFor={`remarks-${student.id}`} className=\"text-sm font-medium text-gray-700\">\n                            Remarks\n                          </Label>\n                          <Input\n                            id={`remarks-${student.id}`}\n                            type=\"text\"\n                            value={marks[student.id]?.remarks || ''}\n                            onChange={(e) => handleMarkChange(student.id, 'remarks', e.target.value)}\n                            className=\"mt-1\"\n                            placeholder=\"Optional\"\n                            maxLength={500}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </Card>\n                )\n              })}\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AA4De,SAAS,eAAe,KAA+D;QAA/D,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,KAAK,EAAuB,GAA/D;;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,0TAAQ;mCAA6D;YAC7F,MAAM,eAA2E,CAAC;YAClF,SAAS,OAAO;2CAAC,CAAC;wBAEC,oCAAA,sBACN;oBAFX,YAAY,CAAC,QAAQ,EAAE,CAAC,GAAG;wBACzB,eAAe,EAAA,uBAAA,QAAQ,WAAW,cAAnB,4CAAA,qCAAA,qBAAqB,aAAa,cAAlC,yDAAA,mCAAoC,QAAQ,OAAM;wBACjE,SAAS,EAAA,wBAAA,QAAQ,WAAW,cAAnB,4CAAA,sBAAqB,OAAO,KAAI;oBAC3C;gBACF;;YACA,OAAO;QACT;;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,0TAAQ,EAAoC,CAAC;IACzE,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,0TAAQ,EAAS;IAEzD,MAAM,mBAAmB,CAAC,WAAmB,OAAoC;QAC/E,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;oBACX,GAAG,IAAI,CAAC,UAAU;oBAClB,CAAC,MAAM,EAAE;gBACX;YACF,CAAC;QAED,uCAAuC;QACvC,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,UAAU,CAAA;gBACR,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,UAAU;gBAC3B,OAAO;YACT;QACF;QAEA,sBAAsB;QACtB,IAAI,cAAc;YAChB,gBAAgB;QAClB;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,YAA+C,CAAC;QACtD,IAAI,YAAY;QAEhB,OAAO,OAAO,CAAC,OAAO,OAAO,CAAC;gBAAC,CAAC,WAAW,KAAK;YAC9C,IAAI,KAAK,aAAa,KAAK,IAAI;gBAC7B,MAAM,gBAAgB,WAAW,KAAK,aAAa;gBACnD,IAAI,CAAC,MAAM,gBAAgB;oBACzB,MAAM,aAAa,IAAA,2LAAiB,EAClC,WACA,KAAK,EAAE,EACP,eACA,KAAK,QAAQ,EACb,KAAK,OAAO;oBAGd,IAAI,CAAC,WAAW,OAAO,EAAE;wBACvB,SAAS,CAAC,UAAU,GAAG,WAAW,MAAM;wBACxC,YAAY;oBACd;gBACF,OAAO;oBACL,SAAS,CAAC,UAAU,GAAG;wBAAC;4BAAE,OAAO;4BAAiB,SAAS;wBAAuB;qBAAE;oBACpF,YAAY;gBACd;YACF;QACF;QAEA,UAAU;QACV,OAAO,CAAC;IACV;IAEA,MAAM,aAAa;QACjB,wBAAwB;QACxB,gBAAgB;QAEhB,IAAI,CAAC,iBAAiB;YACpB,gBAAgB;YAChB;QACF;QAEA,oCAAoC;QACpC,MAAM,YAAY,OAAO,OAAO,CAAC,OAC9B,MAAM,CAAC;gBAAC,CAAC,GAAG,KAAK;mBAAK,KAAK,aAAa,KAAK;WAC7C,GAAG,CAAC;gBAAC,CAAC,WAAW,KAAK;mBAAM;gBAC3B;gBACA,eAAe,WAAW,KAAK,aAAa;gBAC5C,SAAS,KAAK,OAAO,IAAI;YAC3B;;QAEF,IAAI,UAAU,MAAM,KAAK,GAAG;YAC1B,gBAAgB;YAChB;QACF;QAEA,IAAI;YACF,MAAM,OAAO;QACf,EAAE,OAAO,OAAO;YACd,gBAAgB;YAChB,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,qBACE,8UAAC,6KAAI;;0BACH,8UAAC,mLAAU;;kCACT,8UAAC,kLAAS;wBAAC,WAAU;;0CACnB,8UAAC,sUAAK;gCAAC,WAAU;;;;;;0CACjB,8UAAC;;oCAAK;oCAAW,SAAS,MAAM;oCAAC;;;;;;;;;;;;;kCAEnC,8UAAC,wLAAe;;4BAAC;4BAC+B,KAAK,QAAQ;;;;;;;oBAI5D,8BACC,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,4VAAW;gCAAC,WAAU;;;;;;0CACvB,8UAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAK5C,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,mUAAI;gCAAC,WAAU;;;;;;0CAChB,8UAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;kCAK1C,8UAAC;wBAAI,WAAU;kCACb,cAAA,8UAAC,iLAAM;4BAAC,SAAS;4BAAY,UAAU;;8CACrC,8UAAC,mUAAI;oCAAC,WAAU;;;;;;gCACf,SAAS,cAAc;;;;;;;;;;;;;;;;;;0BAI9B,8UAAC,oLAAW;0BACT,SAAS,MAAM,KAAK,kBACnB,8UAAC;oBAAI,WAAU;;sCACb,8UAAC,sUAAK;4BAAC,WAAU;;;;;;sCACjB,8UAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,8UAAC;oBAAI,WAAU;;sCAEb,8UAAC;4BAAI,WAAU;sCACb,cAAA,8UAAC;gCAAM,WAAU;;kDACf,8UAAC;wCAAM,WAAU;kDACf,cAAA,8UAAC;;8DACC,8UAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8UAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8UAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8UAAC;oDAAG,WAAU;;wDAAiF;wDACpF,KAAK,QAAQ;wDAAC;;;;;;;8DAEzB,8UAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8UAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,8UAAC;wCAAM,WAAU;kDACd,SAAS,GAAG,CAAC,CAAC;gDA4BE,mBAgBA;4CA3Cf,MAAM,gBAAgB,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE;4CAC9C,MAAM,YAAY,cAAc,MAAM,GAAG;4CAEzC,qBACE,8UAAC;gDAAoB,WAAW,YAAY,cAAc;;kEACxD,8UAAC;wDAAG,WAAU;kEACZ,cAAA,8UAAC;;8EACC,8UAAC;oEAAI,WAAU;;wEACZ,QAAQ,SAAS;wEAAC;wEAAE,QAAQ,QAAQ;;;;;;;8EAEvC,8UAAC;oEAAI,WAAU;8EACZ,QAAQ,WAAW;;;;;;;;;;;;;;;;;kEAI1B,8UAAC;wDAAG,WAAU;kEACX,QAAQ,UAAU,IAAI;;;;;;kEAEzB,8UAAC;wDAAG,WAAU;kEACX,QAAQ,WAAW,IAAI;;;;;;kEAE1B,8UAAC;wDAAG,WAAU;;0EACZ,8UAAC,+KAAK;gEACJ,MAAK;gEACL,KAAI;gEACJ,KAAK,KAAK,QAAQ;gEAClB,MAAK;gEACL,OAAO,EAAA,oBAAA,KAAK,CAAC,QAAQ,EAAE,CAAC,cAAjB,wCAAA,kBAAmB,aAAa,KAAI;gEAC3C,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAC7E,WAAW,AAAC,QAAyC,OAAlC,YAAY,mBAAmB;gEAClD,aAAY;;;;;;4DAEb,2BACC,8UAAC;gEAAI,WAAU;0EACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8UAAC;kFAAiB,MAAM,OAAO;uEAArB;;;;;;;;;;;;;;;;kEAKlB,8UAAC;wDAAG,WAAU;kEACZ,cAAA,8UAAC,+KAAK;4DACJ,MAAK;4DACL,OAAO,EAAA,qBAAA,KAAK,CAAC,QAAQ,EAAE,CAAC,cAAjB,yCAAA,mBAAmB,OAAO,KAAI;4DACrC,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4DACvE,WAAU;4DACV,aAAY;4DACZ,WAAW;;;;;;;;;;;kEAGf,8UAAC;wDAAG,WAAU;kEACX,QAAQ,QAAQ,iBACf,8UAAC;4DAAK,WAAU;;8EACd,8UAAC,mWAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;iFAI1C,8UAAC;4DAAK,WAAU;sEAAkG;;;;;;;;;;;;+CArD/G,QAAQ,EAAE;;;;;wCA4DvB;;;;;;;;;;;;;;;;;sCAMN,8UAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;oCAyCM,mBAoBA;gCA5DnB,MAAM,gBAAgB,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE;gCAC9C,MAAM,YAAY,cAAc,MAAM,GAAG;gCAEzC,qBACE,8UAAC,6KAAI;oCAAkB,WAAW,AAAC,OAAkD,OAA5C,YAAY,6BAA6B;8CAChF,cAAA,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;wDAAI,WAAU;;0EACb,8UAAC;gEAAG,WAAU;;oEACX,QAAQ,SAAS;oEAAC;oEAAE,QAAQ,QAAQ;;;;;;;0EAEvC,8UAAC;gEAAE,WAAU;;oEACV,QAAQ,WAAW;oEAAC;oEAAU,QAAQ,UAAU,IAAI;oEAAI;oEAAa,QAAQ,WAAW,IAAI;;;;;;;;;;;;;kEAGjG,8UAAC;wDAAI,WAAU;kEACZ,QAAQ,QAAQ,iBACf,8UAAC;4DAAK,WAAU;;8EACd,8UAAC,mWAAW;oEAAC,WAAU;;;;;;gEAAiB;;;;;;iFAI1C,8UAAC;4DAAK,WAAU;sEAAkG;;;;;;;;;;;;;;;;;0DAOxH,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;;0EACC,8UAAC,+KAAK;gEAAC,SAAS,AAAC,SAAmB,OAAX,QAAQ,EAAE;gEAAI,WAAU;;oEAAoC;oEAC1E,KAAK,QAAQ;oEAAC;;;;;;;0EAEzB,8UAAC,+KAAK;gEACJ,IAAI,AAAC,SAAmB,OAAX,QAAQ,EAAE;gEACvB,MAAK;gEACL,KAAI;gEACJ,KAAK,KAAK,QAAQ;gEAClB,MAAK;gEACL,OAAO,EAAA,oBAAA,KAAK,CAAC,QAAQ,EAAE,CAAC,cAAjB,wCAAA,kBAAmB,aAAa,KAAI;gEAC3C,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAC7E,WAAW,AAAC,QAAyC,OAAlC,YAAY,mBAAmB;gEAClD,aAAY;;;;;;4DAEb,2BACC,8UAAC;gEAAI,WAAU;0EACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8UAAC;kFAAiB,MAAM,OAAO;uEAArB;;;;;;;;;;;;;;;;kEAKlB,8UAAC;;0EACC,8UAAC,+KAAK;gEAAC,SAAS,AAAC,WAAqB,OAAX,QAAQ,EAAE;gEAAI,WAAU;0EAAoC;;;;;;0EAGvF,8UAAC,+KAAK;gEACJ,IAAI,AAAC,WAAqB,OAAX,QAAQ,EAAE;gEACzB,MAAK;gEACL,OAAO,EAAA,qBAAA,KAAK,CAAC,QAAQ,EAAE,CAAC,cAAjB,yCAAA,mBAAmB,OAAO,KAAI;gEACrC,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,EAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEACvE,WAAU;gEACV,aAAY;gEACZ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;mCA5DV,QAAQ,EAAE;;;;;4BAmEzB;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA/TwB;KAAA", "debugId": null}}, {"offset": {"line": 2324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/xampp/htdocs/Advance%20School/school-management-system/src/app/%28dash%29/teacher/marks/%5BexamId%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { usePara<PERSON>, useRouter } from 'next/navigation'\nimport { useSession } from 'next-auth/react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport DashboardLayout from '@/components/layout/dashboard-layout'\nimport { teacherNavigation } from '@/lib/navigation'\nimport MarksEntryForm from '@/components/marks/marks-entry-form'\nimport {\n  Calendar,\n  Award,\n  BookOpen,\n  Users,\n  ArrowLeft,\n  AlertCircle\n} from 'lucide-react'\nimport Link from 'next/link'\n\ninterface Student {\n  id: string\n  admissionNo: string\n  rollNumber: string\n  firstName: string\n  lastName: string\n  email: string\n  className: string\n  sectionName: string\n  currentMark: {\n    id: string\n    obtainedMarks: number\n    remarks?: string\n  } | null\n  hasMarks: boolean\n}\n\ninterface Exam {\n  id: string\n  name: string\n  maxMarks: number\n  date: string\n  subject: {\n    id: string\n    name: string\n    code: string\n    class: {\n      id: string\n      name: string\n    }\n  }\n  term: {\n    id: string\n    name: string\n  }\n}\n\ninterface ExamData {\n  exam: Exam\n  students: Student[]\n}\n\n\n\nexport default function TeacherMarksEntryPage() {\n  const params = useParams()\n  const router = useRouter()\n  const { data: session } = useSession()\n  const examId = params.examId as string\n\n  const [examData, setExamData] = useState<ExamData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [saving, setSaving] = useState(false)\n\n  useEffect(() => {\n    fetchExamData()\n  }, [examId])\n\n  const fetchExamData = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/teacher/exams/${examId}/students`)\n      if (response.ok) {\n        const data = await response.json()\n        setExamData(data)\n      } else {\n        console.error('Failed to fetch exam data')\n      }\n    } catch (error) {\n      console.error('Error fetching exam data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSaveMarks = async (marksData: Array<{studentId: string, obtainedMarks: number, remarks?: string}>) => {\n    try {\n      setSaving(true)\n\n      const response = await fetch('/api/teacher/marks', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          examId,\n          marks: marksData\n        })\n      })\n\n      if (response.ok) {\n        const result = await response.json()\n        const successCount = result.results.filter((r: any) => r.success).length\n        const errorCount = result.results.filter((r: any) => !r.success).length\n\n        if (errorCount === 0) {\n          alert(`Successfully saved marks for ${successCount} students`)\n          fetchExamData() // Refresh data\n        } else {\n          alert(`Saved marks for ${successCount} students. ${errorCount} failed.`)\n          console.error('Some marks failed to save:', result.results.filter((r: any) => !r.success))\n        }\n      } else {\n        const error = await response.json()\n        alert(`Failed to save marks: ${error.error}`)\n      }\n    } catch (error) {\n      console.error('Error saving marks:', error)\n      alert('Failed to save marks. Please try again.')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"Enter Marks\" navigation={teacherNavigation}>\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-2 text-gray-600\">Loading exam data...</p>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  if (!examData) {\n    return (\n      <DashboardLayout title=\"Enter Marks\" navigation={teacherNavigation}>\n        <div className=\"text-center py-8\">\n          <AlertCircle className=\"h-12 w-12 text-red-400 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Exam not found</p>\n          <Link href=\"/teacher/marks\">\n            <Button className=\"mt-4\">\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\n              Back to Marks\n            </Button>\n          </Link>\n        </div>\n      </DashboardLayout>\n    )\n  }\n\n  return (\n    <DashboardLayout title=\"Enter Marks\" navigation={teacherNavigation}>\n      <div className=\"space-y-6\">\n        {/* Header */}\n        <div className=\"flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0\">\n          <div>\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Link href=\"/teacher/marks\">\n                <Button variant=\"outline\" size=\"sm\">\n                  <ArrowLeft className=\"w-4 h-4 mr-1\" />\n                  Back\n                </Button>\n              </Link>\n            </div>\n            <h1 className=\"text-xl sm:text-2xl font-bold text-gray-900\">Enter Marks</h1>\n            <p className=\"text-sm sm:text-base text-gray-600\">\n              {examData.exam.name} - {examData.exam.subject.name} ({examData.exam.subject.class.name})\n            </p>\n          </div>\n        </div>\n\n        {/* Exam Info */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <BookOpen className=\"w-5 h-5\" />\n              <span>Exam Details</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 text-sm\">\n              <div>\n                <span className=\"font-medium text-gray-700\">Subject:</span>\n                <p className=\"text-gray-600\">{examData.exam.subject.name}</p>\n              </div>\n              <div>\n                <span className=\"font-medium text-gray-700\">Class:</span>\n                <p className=\"text-gray-600\">{examData.exam.subject.class.name}</p>\n              </div>\n              <div>\n                <span className=\"font-medium text-gray-700\">Max Marks:</span>\n                <p className=\"text-gray-600\">{examData.exam.maxMarks}</p>\n              </div>\n              <div>\n                <span className=\"font-medium text-gray-700\">Date:</span>\n                <p className=\"text-gray-600\">{new Date(examData.exam.date).toLocaleDateString()}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Marks Entry Form */}\n        <MarksEntryForm\n          exam={examData.exam}\n          students={examData.students}\n          onSave={handleSaveMarks}\n          saving={saving}\n        />\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAQA;;;AAlBA;;;;;;;;;;;AAgEe,SAAS;;IACtB,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,SAAS,IAAA,mSAAS;IACxB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,6SAAU;IACpC,MAAM,SAAS,OAAO,MAAM;IAE5B,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,0TAAQ,EAAkB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,0TAAQ,EAAC;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,0TAAQ,EAAC;IAErC,IAAA,2TAAS;2CAAC;YACR;QACF;0CAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,AAAC,sBAA4B,OAAP,QAAO;YAC1D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY;YACd,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,UAAU;YAEV,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,OAAO;gBACT;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,MAAM,eAAe,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAW,EAAE,OAAO,EAAE,MAAM;gBACxE,MAAM,aAAa,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAW,CAAC,EAAE,OAAO,EAAE,MAAM;gBAEvE,IAAI,eAAe,GAAG;oBACpB,MAAM,AAAC,gCAA4C,OAAb,cAAa;oBACnD,iBAAgB,eAAe;gBACjC,OAAO;oBACL,MAAM,AAAC,mBAA4C,OAA1B,cAAa,eAAwB,OAAX,YAAW;oBAC9D,QAAQ,KAAK,CAAC,8BAA8B,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAW,CAAC,EAAE,OAAO;gBAC1F;YACF,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,AAAC,yBAAoC,OAAZ,MAAM,KAAK;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8UAAC,mMAAe;YAAC,OAAM;YAAc,YAAY,kLAAiB;sBAChE,cAAA,8UAAC;gBAAI,WAAU;;kCACb,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,8UAAC,mMAAe;YAAC,OAAM;YAAc,YAAY,kLAAiB;sBAChE,cAAA,8UAAC;gBAAI,WAAU;;kCACb,8UAAC,4VAAW;wBAAC,WAAU;;;;;;kCACvB,8UAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8UAAC,2TAAI;wBAAC,MAAK;kCACT,cAAA,8UAAC,iLAAM;4BAAC,WAAU;;8CAChB,8UAAC,sVAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,8UAAC,mMAAe;QAAC,OAAM;QAAc,YAAY,kLAAiB;kBAChE,cAAA,8UAAC;YAAI,WAAU;;8BAEb,8UAAC;oBAAI,WAAU;8BACb,cAAA,8UAAC;;0CACC,8UAAC;gCAAI,WAAU;0CACb,cAAA,8UAAC,2TAAI;oCAAC,MAAK;8CACT,cAAA,8UAAC,iLAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8UAAC,sVAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;0CAK5C,8UAAC;gCAAG,WAAU;0CAA8C;;;;;;0CAC5D,8UAAC;gCAAE,WAAU;;oCACV,SAAS,IAAI,CAAC,IAAI;oCAAC;oCAAI,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI;oCAAC;oCAAG,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI;oCAAC;;;;;;;;;;;;;;;;;;8BAM7F,8UAAC,6KAAI;;sCACH,8UAAC,mLAAU;sCACT,cAAA,8UAAC,kLAAS;gCAAC,WAAU;;kDACnB,8UAAC,mVAAQ;wCAAC,WAAU;;;;;;kDACpB,8UAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8UAAC,oLAAW;sCACV,cAAA,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;;0DACC,8UAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8UAAC;gDAAE,WAAU;0DAAiB,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI;;;;;;;;;;;;kDAE1D,8UAAC;;0DACC,8UAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8UAAC;gDAAE,WAAU;0DAAiB,SAAS,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI;;;;;;;;;;;;kDAEhE,8UAAC;;0DACC,8UAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8UAAC;gDAAE,WAAU;0DAAiB,SAAS,IAAI,CAAC,QAAQ;;;;;;;;;;;;kDAEtD,8UAAC;;0DACC,8UAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8UAAC;gDAAE,WAAU;0DAAiB,IAAI,KAAK,SAAS,IAAI,CAAC,IAAI,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOrF,8UAAC,qMAAc;oBACb,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,QAAQ;oBACR,QAAQ;;;;;;;;;;;;;;;;;AAKlB;GA/JwB;;QACP,mSAAS;QACT,mSAAS;QACE,6SAAU;;;KAHd", "debugId": null}}]}